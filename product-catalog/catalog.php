<?php
include __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/functions.php';

$category = isset($_GET['category']) ? sanitize_category($_GET['category']) : null;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

try {
    $sql = "SELECT * FROM products WHERE 1=1";
    $params = [];

    if ($category) {
        $sql .= " AND category = ?";
        $params[] = $category;
    }

    if ($search) {
        $sql .= " AND (name LIKE ? OR description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    $sql .= " ORDER BY created_at DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll();

    // Get product count by category for display
    $categoryStats = [];
    $statsStmt = $pdo->query("SELECT category, COUNT(*) as count FROM products GROUP BY category ORDER BY category");
    while ($row = $statsStmt->fetch()) {
        $categoryStats[$row['category']] = $row['count'];
    }

} catch (Exception $e) {
    $products = [];
    $categoryStats = [];
    $error = $e->getMessage();
}
?>
<section class="card">
  <h2>Product Catalog</h2>
  <p>Discover exceptional products crafted by our talented students across various technical trades.</p>
</section>

<section class="card mt-1">
  <h3>Search & Filter</h3>
  <form method="get" class="filters">
    <div style="display: grid; grid-template-columns: 1fr 1fr auto; gap: 0.5rem; align-items: end;">
      <label>
        Search Products
        <input type="text" name="search" placeholder="Search by name or description..." value="<?= htmlspecialchars($search) ?>">
      </label>

      <label>
        Filter by Trade
        <select name="category">
          <option value="">All Trades</option>
          <?php
            $cats = ['TFD','Plumbing','FBW','Electrical','ICT','Welding','Carpentry','Other'];
            foreach ($cats as $cat) {
              $sel = ($category === $cat) ? 'selected' : '';
              $count = isset($categoryStats[$cat]) ? " ({$categoryStats[$cat]})" : '';
              echo "<option value='".htmlspecialchars($cat)."' $sel>".htmlspecialchars($cat).$count."</option>";
            }
          ?>
        </select>
      </label>

      <button type="submit" class="btn">Search</button>
    </div>

    <?php if ($category || $search): ?>
      <div class="mt-1">
        <a href="/catalog.php" class="btn btn-secondary">Clear Filters</a>
        <?php if ($category): ?>
          <span class="filter-tag">Trade: <?= htmlspecialchars($category) ?></span>
        <?php endif; ?>
        <?php if ($search): ?>
          <span class="filter-tag">Search: "<?= htmlspecialchars($search) ?>"</span>
        <?php endif; ?>
      </div>
    <?php endif; ?>
  </form>
</section>

<?php if (!empty($error)): ?>
  <div class="error"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>

<section class="mt-1">
  <?php if (count($products) > 0): ?>
    <div class="card mb-1">
      <h3>Products Found: <?= count($products) ?></h3>
      <?php if ($category || $search): ?>
        <p>
          <?php if ($category && $search): ?>
            Showing products in <strong><?= htmlspecialchars($category) ?></strong> matching "<strong><?= htmlspecialchars($search) ?></strong>"
          <?php elseif ($category): ?>
            Showing products in <strong><?= htmlspecialchars($category) ?></strong> trade
          <?php elseif ($search): ?>
            Showing products matching "<strong><?= htmlspecialchars($search) ?></strong>"
          <?php endif; ?>
        </p>
      <?php endif; ?>
    </div>

    <div class="grid">
      <?php foreach ($products as $p): ?>
        <article class="card">
          <?php if (!empty($p['image_path'])): ?>
            <img src="<?= htmlspecialchars($p['image_path']) ?>" alt="<?= htmlspecialchars($p['name']) ?>">
          <?php else: ?>
            <div class="img-placeholder">
              <span style="font-size: 2rem;">📦</span><br>
              No Image Available
            </div>
          <?php endif; ?>
          <div class="card-body">
            <h3><?= htmlspecialchars($p['name']) ?></h3>
            <p class="muted">
              <strong><?= htmlspecialchars($p['category']) ?></strong> •
              Added <?= date('M j, Y', strtotime($p['created_at'])) ?>
            </p>
            <p><?= nl2br(htmlspecialchars(mb_strimwidth($p['description'],0,150,'...'))) ?></p>
            <details>
              <summary style="cursor: pointer; color: #0b57d0; font-weight: bold;">View Full Description</summary>
              <div style="margin-top: 0.5rem; padding-top: 0.5rem; border-top: 1px solid #eee;">
                <p><?= nl2br(htmlspecialchars($p['description'])) ?></p>
                <?php if (is_logged_in()): ?>
                  <p class="muted" style="margin-top: 1rem;">
                    <strong>Interested in this product?</strong>
                    <a href="/contact.php" style="color: #0b57d0;">Contact us</a> for more information or to place an order.
                  </p>
                <?php else: ?>
                  <p class="muted" style="margin-top: 1rem;">
                    <a href="/login.php" style="color: #0b57d0;">Login</a> or
                    <a href="/register.php" style="color: #0b57d0;">register</a> to inquire about this product.
                  </p>
                <?php endif; ?>
              </div>
            </details>
          </div>
        </article>
      <?php endforeach; ?>
    </div>
  <?php else: ?>
    <div class="card text-center">
      <h3>No Products Found</h3>
      <?php if ($category || $search): ?>
        <p>No products match your current search criteria.</p>
        <a href="/catalog.php" class="btn">View All Products</a>
      <?php else: ?>
        <p>No products have been added to the catalog yet.</p>
        <?php if (is_admin()): ?>
          <a href="/admin/products.php" class="btn">Add Products</a>
        <?php endif; ?>
      <?php endif; ?>
    </div>
  <?php endif; ?>
</section>
<?php include __DIR__ . '/../includes/footer.php'; ?>
