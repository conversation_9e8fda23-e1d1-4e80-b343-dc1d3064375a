<?php
include __DIR__ . '/../../includes/header.php';
require_once __DIR__ . '/../../includes/functions.php';
require_login();
require_admin();

// Handle create/update/delete
$action = $_GET['action'] ?? null;
$editing = null;

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        verify_csrf();
        $name = trim($_POST['name'] ?? '');
        $category = sanitize_category($_POST['category'] ?? 'Other');
        $description = trim($_POST['description'] ?? '');
        $id = isset($_POST['id']) ? (int)$_POST['id'] : null;

        // basic validation
        if ($name === '' || $description === '') {
            throw new RuntimeException('Name and description are required.');
        }

        // image upload (optional)
        $image_path = null;
        if (!empty($_FILES['image']['name'])) {
            $image_path = upload_product_image($_FILES['image']);
        }

        if ($id) {
            if ($image_path) {
                $stmt = $pdo->prepare("UPDATE products SET name=?, category=?, description=?, image_path=? WHERE id=?");
                $stmt->execute([$name, $category, $description, $image_path, $id]);
            } else {
                $stmt = $pdo->prepare("UPDATE products SET name=?, category=?, description=? WHERE id=?");
                $stmt->execute([$name, $category, $description, $id]);
            }
            $msg = "Product updated.";
        } else {
            $stmt = $pdo->prepare("INSERT INTO products (name, category, description, image_path, created_by) VALUES (?,?,?,?,?)");
            $stmt->execute([$name, $category, $description, $image_path, $_SESSION['user']['id'] ?? null]);
            $msg = "Product created.";
        }
        header("Location: /admin/products.php?msg=" . urlencode($msg));
        exit;
    }

    if ($action === 'edit' && isset($_GET['id'])) {
        $stmt = $pdo->prepare("SELECT * FROM products WHERE id=?");
        $stmt->execute([(int)$_GET['id']]);
        $editing = $stmt->fetch();
        if (!$editing) throw new RuntimeException("Product not found.");
    }

    if ($action === 'delete' && isset($_GET['id']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
        verify_csrf();
        $stmt = $pdo->prepare("DELETE FROM products WHERE id=?");
        $stmt->execute([(int)$_GET['id']]);
        header("Location: /admin/products.php?msg=" . urlencode("Product deleted."));
        exit;
    }

    $products = $pdo->query("SELECT * FROM products ORDER BY created_at DESC")->fetchAll();
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>
<section class="card">
  <h2>Manage Products</h2>
  <p>Add, edit, and manage products in your catalog. Upload images and organize by trade categories.</p>
  <div>
    <a href="/admin/dashboard.php" class="btn btn-secondary">← Back to Dashboard</a>
    <a href="/catalog.php" class="btn btn-secondary">View Public Catalog</a>
  </div>
</section>

<?php if (!empty($_GET['msg'])): ?>
  <div class="success mt-1"><?= htmlspecialchars($_GET['msg']) ?></div>
<?php endif; ?>
<?php if (!empty($error)): ?>
  <div class="error mt-1"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>

<section class="admin-grid mt-1">
  <form method="post" enctype="multipart/form-data" class="card">
    <h3><?= $editing ? 'Edit Product' : 'Add New Product' ?></h3>
    <?php if ($editing): ?>
      <p class="muted">Editing: <strong><?= htmlspecialchars($editing['name']) ?></strong></p>
    <?php endif; ?>

    <?php csrf_field(); ?>
    <?php if ($editing): ?><input type="hidden" name="id" value="<?= (int)$editing['id'] ?>"><?php endif; ?>

    <label>Product Name *
      <input type="text" name="name" required value="<?= htmlspecialchars($editing['name'] ?? '') ?>"
             placeholder="Enter product name...">
    </label>

    <label>Trade Category *
      <select name="category" required>
        <option value="">Select a trade category...</option>
        <?php
          $cats = ['TFD','Plumbing','FBW','Electrical','ICT','Welding','Carpentry','Other'];
          foreach ($cats as $cat) {
            $sel = ($editing && $editing['category'] === $cat) ? 'selected' : '';
            echo "<option value='".htmlspecialchars($cat)."' $sel>".htmlspecialchars($cat)."</option>";
          }
        ?>
      </select>
    </label>

    <label>Product Description *
      <textarea name="description" rows="6" required placeholder="Describe the product, its features, and how it was made..."><?= htmlspecialchars($editing['description'] ?? '') ?></textarea>
    </label>

    <label>Product Image (Optional)
      <input type="file" name="image" accept="image/jpeg,image/png,image/webp">
      <small class="muted">Supported formats: JPEG, PNG, WebP. Max file size: 5MB</small>
      <?php if ($editing && !empty($editing['image_path'])): ?>
        <div style="margin-top: 0.5rem;">
          <p class="muted">Current image:</p>
          <img src="<?= htmlspecialchars($editing['image_path']) ?>" alt="Current product image"
               style="max-width: 200px; max-height: 150px; border-radius: 8px;">
        </div>
      <?php endif; ?>
    </label>

    <div style="display: flex; gap: 0.5rem;">
      <button type="submit" class="btn"><?= $editing ? 'Update Product' : 'Create Product' ?></button>
      <?php if ($editing): ?>
        <a href="/admin/products.php" class="btn btn-secondary">Cancel Edit</a>
      <?php endif; ?>
    </div>
  </form>

  <div class="card">
    <h3>Existing Products (<?= count($products) ?>)</h3>

    <?php if (count($products) > 0): ?>
      <div style="overflow-x: auto;">
        <table class="table">
          <thead>
            <tr>
              <th>Image</th>
              <th>Name</th>
              <th>Category</th>
              <th>Description</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <?php foreach ($products as $p): ?>
              <tr>
                <td>
                  <?php if (!empty($p['image_path'])): ?>
                    <img src="<?= htmlspecialchars($p['image_path']) ?>" alt="Product image"
                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 6px;">
                  <?php else: ?>
                    <div style="width: 50px; height: 50px; background: #f0f0f0; border-radius: 6px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">📦</div>
                  <?php endif; ?>
                </td>
                <td><strong><?= htmlspecialchars($p['name']) ?></strong></td>
                <td><span class="filter-tag"><?= htmlspecialchars($p['category']) ?></span></td>
                <td><?= htmlspecialchars(mb_strimwidth($p['description'], 0, 80, '...')) ?></td>
                <td><?= date('M j, Y', strtotime($p['created_at'])) ?></td>
                <td>
                  <div style="display: flex; gap: 0.25rem; flex-wrap: wrap;">
                    <a href="/admin/products.php?action=edit&id=<?= (int)$p['id'] ?>" class="btn" style="padding: 0.3rem 0.5rem; font-size: 0.8rem;">Edit</a>
                    <form action="/admin/products.php?action=delete&id=<?= (int)$p['id'] ?>" method="post" class="inline">
                      <?php csrf_field(); ?>
                      <button type="submit" class="btn btn-danger" style="padding: 0.3rem 0.5rem; font-size: 0.8rem;"
                              onclick="return confirm('Are you sure you want to delete &quot;<?= htmlspecialchars($p['name']) ?>&quot;? This action cannot be undone.')">Delete</button>
                    </form>
                  </div>
                </td>
              </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      </div>
    <?php else: ?>
      <div class="text-center" style="padding: 2rem;">
        <p>No products have been added yet.</p>
        <p class="muted">Create your first product using the form on the left.</p>
      </div>
    <?php endif; ?>
  </div>
</section>
<?php include __DIR__ . '/../../includes/footer.php'; ?>
