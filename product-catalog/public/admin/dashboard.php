<?php
include __DIR__ . '/../../includes/header.php';
require_once __DIR__ . '/../../includes/functions.php';
require_login();
require_admin();

// Get dashboard statistics
try {
    $totalProducts = $pdo->query("SELECT COUNT(*) FROM products")->fetchColumn();
    $totalUsers = $pdo->query("SELECT COUNT(*) FROM users WHERE role != 'admin'")->fetchColumn();
    $recentProducts = $pdo->query("SELECT COUNT(*) FROM products WHERE created_at >= datetime('now', '-7 days')")->fetchColumn();

    // Get products by category
    $categoryStats = [];
    $stmt = $pdo->query("SELECT category, COUNT(*) as count FROM products GROUP BY category ORDER BY count DESC");
    while ($row = $stmt->fetch()) {
        $categoryStats[$row['category']] = $row['count'];
    }

    // Get recent activity
    $recentActivity = $pdo->query("SELECT name, category, created_at FROM products ORDER BY created_at DESC LIMIT 5")->fetchAll();

} catch (Exception $e) {
    $error = $e->getMessage();
}
?>

<section class="card">
  <h2>Admin Dashboard</h2>
  <p>Welcome, <?= htmlspecialchars($_SESSION['user']['first_name']) ?>! Here's an overview of your catalog.</p>
</section>

<?php if (!empty($error)): ?>
  <div class="error mt-1"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>

<section class="grid mt-1">
  <div class="card text-center">
    <h3 style="color: #0b57d0; font-size: 2rem; margin: 0;"><?= $totalProducts ?? 0 ?></h3>
    <p>Total Products</p>
    <a href="/admin/products.php" class="btn">Manage Products</a>
  </div>

  <div class="card text-center">
    <h3 style="color: #0b57d0; font-size: 2rem; margin: 0;"><?= $totalUsers ?? 0 ?></h3>
    <p>Registered Users</p>
  </div>

  <div class="card text-center">
    <h3 style="color: #0b57d0; font-size: 2rem; margin: 0;"><?= $recentProducts ?? 0 ?></h3>
    <p>Products Added This Week</p>
  </div>
</section>

<?php if (!empty($categoryStats)): ?>
<section class="card mt-1">
  <h3>Products by Trade Category</h3>
  <div class="grid">
    <?php foreach ($categoryStats as $category => $count): ?>
      <div class="card">
        <h4><?= htmlspecialchars($category) ?></h4>
        <p style="font-size: 1.5rem; color: #0b57d0; margin: 0;"><?= $count ?> products</p>
        <a href="/catalog.php?category=<?= urlencode($category) ?>" class="btn btn-secondary">View Products</a>
      </div>
    <?php endforeach; ?>
  </div>
</section>
<?php endif; ?>

<?php if (!empty($recentActivity)): ?>
<section class="card mt-1">
  <h3>Recent Product Activity</h3>
  <table class="table">
    <thead>
      <tr>
        <th>Product Name</th>
        <th>Category</th>
        <th>Added</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach ($recentActivity as $product): ?>
        <tr>
          <td><?= htmlspecialchars($product['name']) ?></td>
          <td><?= htmlspecialchars($product['category']) ?></td>
          <td><?= date('M j, Y g:i A', strtotime($product['created_at'])) ?></td>
        </tr>
      <?php endforeach; ?>
    </tbody>
  </table>
  <div class="text-center mt-1">
    <a href="/admin/products.php" class="btn">View All Products</a>
  </div>
</section>
<?php endif; ?>

<section class="card mt-1">
  <h3>Quick Actions</h3>
  <div class="grid">
    <a href="/admin/products.php" class="btn">Add New Product</a>
    <a href="/catalog.php" class="btn btn-secondary">View Public Catalog</a>
    <a href="/index.php" class="btn btn-secondary">Back to Home</a>
  </div>
</section>

<?php include __DIR__ . '/../../includes/footer.php'; ?>
