/* Basic responsive, clean design */
:root { --maxw: 1100px; --pad: 1rem; --radius: 14px; }

* { box-sizing: border-box; }
body { margin:0; font-family: system-ui, Arial, sans-serif; line-height: 1.5; color:#222; background:#f7f7f8; }
.container { max-width: var(--maxw); margin: 0 auto; padding: var(--pad); }
.site-header, .site-footer { background: white; border-bottom: 1px solid #eee; }
.site-header { display:flex; justify-content: space-between; align-items: center; padding: var(--pad); gap:1rem; }
.brand { display:flex; align-items:center; gap:1rem; }
.logo { width:64px; height:64px; object-fit: contain; }
.contact-top { text-align:right; font-size: .9rem; }
.nav { display:flex; gap: .75rem; background:white; border-bottom:1px solid #eee; padding: .5rem var(--pad); align-items:center; }
.nav a { text-decoration:none; color:#0b57d0; padding:.4rem .6rem; border-radius:8px; }
.nav a:hover { background:#eef3fe; }
.nav .nav-right { margin-left:auto; color:#666; }

.hero { background:white; padding: var(--pad); border-radius: var(--radius); box-shadow: 0 1px 2px rgba(0,0,0,.05); }
.lead { margin-top: .75rem; }

.slider { position: relative; overflow: hidden; border-radius: var(--radius); }
.slides { display:flex; transition: transform .4s ease; }
.slides img { width:100%; height: 360px; object-fit: cover; flex: 0 0 100%; }
.nav.prev, .nav.next {
  position: absolute; top: 50%; transform: translateY(-50%);
  background: rgba(0,0,0,.4); color:white; border:none; width:40px; height:40px; border-radius:50%;
  font-size: 1.5rem; cursor: pointer;
}
.nav.prev { left: .5rem; }
.nav.next { right: .5rem; }

.grid { display:grid; grid-template-columns: repeat(auto-fit,minmax(240px,1fr)); gap: 1rem; }
.card { background:white; border-radius: var(--radius); padding: var(--pad); box-shadow: 0 1px 2px rgba(0,0,0,.06); }
.card .img-placeholder { background:#ececec; height:160px; border-radius: 10px; display:grid; place-items:center; color:#666; }
.card img { width:100%; height: 160px; object-fit: cover; border-radius: 10px; }
.card .muted { color:#666; font-size:.9rem; }

.admin-links a { color:#0b57d0; }
.table { width:100%; border-collapse: collapse; }
.table th, .table td { border-bottom:1px solid #eee; padding:.5rem; text-align:left; font-size:.95rem; }
.inline { display:inline; }
.filters { display:flex; gap:.5rem; align-items:center; background:white; padding:.5rem; border-radius:10px; margin-bottom:1rem; }
.auth-form { background:white; padding: var(--pad); border-radius: var(--radius); display:grid; gap:.5rem; max-width:480px; }
.auth-form label { display:grid; gap:.25rem; }
.auth-form input, .auth-form select, .auth-form textarea { padding:.6rem; border:1px solid #ddd; border-radius:10px; }
.auth-form button { padding:.6rem .9rem; border:none; background:#0b57d0; color:white; border-radius:10px; cursor:pointer; }
.auth-form button:hover { opacity:.9; }

.site-footer { padding: var(--pad); border-top: 1px solid #eee; }
.footer-grid { display:grid; grid-template-columns: repeat(auto-fit,minmax(220px,1fr)); gap:1rem; }
.map-img, .map-large { width:100%; border-radius: 10px; }
.copy { text-align:center; color:#666; margin-top:.5rem; }

.success { background:#e6f4ea; color:#035e1a; padding:.6rem; border-radius:10px; }
.error { background:#fdecea; color:#b3261e; padding:.6rem; border-radius:10px; }
/* Enhanced responsive design */
@media (max-width: 768px) {
  .site-header { flex-direction: column; text-align: center; }
  .contact-top { text-align: center; margin-top: 1rem; }
  .nav { flex-wrap: wrap; justify-content: center; }
  .footer-grid { grid-template-columns: 1fr; text-align: center; }
  .slides img { height: 220px; }
  .grid { grid-template-columns: 1fr; }
  .admin-grid { display: block; }
  .admin-grid .card { margin-bottom: 1rem; }
}

@media (max-width: 480px) {
  .container { padding: 0.5rem; }
  .site-header, .nav { padding: 0.5rem; }
  .card { padding: 0.75rem; }
  .slides img { height: 180px; }
  .nav a { padding: 0.3rem 0.5rem; font-size: 0.9rem; }
  .table { font-size: 0.85rem; }
  .table th, .table td { padding: 0.3rem; }
}

/* Additional utility classes */
.admin-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; }
.text-center { text-align: center; }
.mb-1 { margin-bottom: 1rem; }
.mt-1 { margin-top: 1rem; }

/* Enhanced button styles */
.btn {
  display: inline-block;
  padding: 0.6rem 1rem;
  background: #0b57d0;
  color: white;
  text-decoration: none;
  border-radius: 10px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}
.btn:hover { background: #0842a0; transform: translateY(-1px); }
.btn-secondary { background: #666; }
.btn-secondary:hover { background: #555; }
.btn-danger { background: #b3261e; }
.btn-danger:hover { background: #8e1e16; }

/* Filter tags */
.filter-tag {
  display: inline-block;
  background: #e8f0fe;
  color: #0b57d0;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  font-size: 0.85rem;
  margin-right: 0.5rem;
}

/* Enhanced form validation */
.error-field {
  border-color: #b3261e !important;
  background-color: #fdecea !important;
}

/* Lazy loading images */
img.lazy {
  opacity: 0;
  transition: opacity 0.3s;
}

/* Mobile navigation */
.nav.mobile-open {
  flex-direction: column;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #eee;
  border-top: none;
  z-index: 100;
}

/* Slide indicators */
.slide-indicators {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  margin-top: 1rem;
}

.slide-indicators button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(11, 87, 208, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.slide-indicators button.active,
.slide-indicators button:hover {
  background: #0b57d0;
  transform: scale(1.2);
}

/* Loading states */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Notification animations */
@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideOutRight {
  from { transform: translateX(0); }
  to { transform: translateX(100%); }
}

.notification {
  animation: slideInRight 0.3s ease;
}

/* Enhanced hover effects */
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,.1);
  transition: all 0.3s ease;
}

.btn:active {
  transform: translateY(0);
}

/* Improved focus states for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #0b57d0;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .nav, .site-footer, .btn, button {
    display: none !important;
  }
  .card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
