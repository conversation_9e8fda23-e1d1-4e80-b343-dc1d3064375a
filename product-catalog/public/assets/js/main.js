// Enhanced JavaScript for Product Catalog Platform
document.addEventListener('DOMContentLoaded', () => {

  // Image Slider with enhanced features
  const slider = document.getElementById('slider');
  if (slider) {
    const track = slider.querySelector('.slides');
    const slides = Array.from(track.children);
    const prev = document.getElementById('prev');
    const next = document.getElementById('next');
    let index = 0;
    let autoRotateInterval;

    function update() {
      track.style.transform = `translateX(-${index * 100}%)`;

      // Add slide indicators if they don't exist
      if (!slider.querySelector('.slide-indicators')) {
        const indicators = document.createElement('div');
        indicators.className = 'slide-indicators';
        indicators.style.cssText = `
          position: absolute; bottom: 1rem; left: 50%; transform: translateX(-50%);
          display: flex; gap: 0.5rem; z-index: 10;
        `;

        slides.forEach((_, i) => {
          const dot = document.createElement('button');
          dot.style.cssText = `
            width: 12px; height: 12px; border-radius: 50%; border: none;
            background: ${i === index ? 'white' : 'rgba(255,255,255,0.5)'};
            cursor: pointer; transition: all 0.3s ease;
          `;
          dot.addEventListener('click', () => {
            index = i;
            update();
            resetAutoRotate();
          });
          indicators.appendChild(dot);
        });

        slider.appendChild(indicators);
      }

      // Update indicators
      const indicators = slider.querySelectorAll('.slide-indicators button');
      indicators.forEach((dot, i) => {
        dot.style.background = i === index ? 'white' : 'rgba(255,255,255,0.5)';
      });
    }

    function resetAutoRotate() {
      clearInterval(autoRotateInterval);
      autoRotateInterval = setInterval(() => {
        index = (index + 1) % slides.length;
        update();
      }, 5000);
    }

    prev.addEventListener('click', () => {
      index = (index - 1 + slides.length) % slides.length;
      update();
      resetAutoRotate();
    });

    next.addEventListener('click', () => {
      index = (index + 1) % slides.length;
      update();
      resetAutoRotate();
    });

    // Pause on hover
    slider.addEventListener('mouseenter', () => clearInterval(autoRotateInterval));
    slider.addEventListener('mouseleave', resetAutoRotate);

    // Touch/swipe support for mobile
    let startX = 0;
    let isDragging = false;

    slider.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
      isDragging = true;
      clearInterval(autoRotateInterval);
    });

    slider.addEventListener('touchmove', (e) => {
      if (!isDragging) return;
      e.preventDefault();
    });

    slider.addEventListener('touchend', (e) => {
      if (!isDragging) return;
      isDragging = false;

      const endX = e.changedTouches[0].clientX;
      const diff = startX - endX;

      if (Math.abs(diff) > 50) { // Minimum swipe distance
        if (diff > 0) {
          index = (index + 1) % slides.length;
        } else {
          index = (index - 1 + slides.length) % slides.length;
        }
        update();
      }

      resetAutoRotate();
    });

    // Initialize
    update();
    resetAutoRotate();
  }

  // Enhanced form validation
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', (e) => {
      const requiredFields = form.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        field.classList.remove('error-field');
        if (!field.value.trim()) {
          field.classList.add('error-field');
          isValid = false;
        }
      });

      if (!isValid) {
        e.preventDefault();
        showNotification('Please fill in all required fields.', 'error');
      }
    });
  });

  // Smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Image lazy loading for better performance
  const images = document.querySelectorAll('img[data-src]');
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove('lazy');
        observer.unobserve(img);
      }
    });
  });

  images.forEach(img => imageObserver.observe(img));

  // Search functionality enhancement
  const searchInput = document.querySelector('input[name="search"]');
  if (searchInput) {
    let searchTimeout;
    searchInput.addEventListener('input', () => {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        // Could implement live search here
        console.log('Search:', searchInput.value);
      }, 300);
    });
  }

  // Mobile menu toggle (if needed)
  const nav = document.querySelector('.nav');
  if (nav && window.innerWidth <= 768) {
    const menuToggle = document.createElement('button');
    menuToggle.innerHTML = '☰';
    menuToggle.style.cssText = `
      display: none; background: none; border: none; font-size: 1.5rem;
      cursor: pointer; padding: 0.5rem;
    `;

    // Add responsive menu functionality for very small screens
    if (window.innerWidth <= 480) {
      menuToggle.style.display = 'block';
      nav.insertBefore(menuToggle, nav.firstChild);

      menuToggle.addEventListener('click', () => {
        nav.classList.toggle('mobile-open');
      });
    }
  }

  // Notification system
  window.showNotification = (message, type = 'info') => {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
      position: fixed; top: 1rem; right: 1rem; z-index: 1000;
      padding: 1rem; border-radius: 8px; color: white; max-width: 300px;
      background: ${type === 'error' ? '#b3261e' : type === 'success' ? '#0f5132' : '#0b57d0'};
      box-shadow: 0 4px 12px rgba(0,0,0,0.15); transform: translateX(100%);
      transition: transform 0.3s ease;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    // Auto remove
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 4000);
  };

  // Add loading states to buttons
  document.querySelectorAll('button[type="submit"]').forEach(button => {
    button.addEventListener('click', () => {
      const originalText = button.textContent;
      button.textContent = 'Loading...';
      button.disabled = true;

      // Re-enable after form submission or timeout
      setTimeout(() => {
        button.textContent = originalText;
        button.disabled = false;
      }, 2000);
    });
  });

  // Enhanced table responsiveness
  const tables = document.querySelectorAll('.table');
  tables.forEach(table => {
    if (!table.parentNode.classList.contains('table-responsive')) {
      const wrapper = document.createElement('div');
      wrapper.className = 'table-responsive';
      wrapper.style.overflowX = 'auto';
      table.parentNode.insertBefore(wrapper, table);
      wrapper.appendChild(table);
    }
  });

});
