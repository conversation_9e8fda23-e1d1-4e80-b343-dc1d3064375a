<?php
include __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/functions.php';

$msg = isset($_GET['msg']) ? $_GET['msg'] : null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    verify_csrf();
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    $user = find_user_by_username($pdo, $username);
    if ($user && password_verify($password, $user['password_hash'])) {
        $_SESSION['user'] = $user;
        $success = "Login successful. Welcome, " . htmlspecialchars($user['first_name']) . "!";
        header("Location: /index.php?msg=" . urlencode($success));
        exit;
    } else {
        $error = "Invalid username or password.";
    }
}
?>
<h2>Login</h2>
<?php if (!empty($msg)): ?><p class="success"><?= htmlspecialchars($msg) ?></p><?php endif; ?>
<?php if (!empty($error)): ?><p class="error"><?= htmlspecialchars($error) ?></p><?php endif; ?>
<form method="post" class="auth-form" novalidate>
  <?php csrf_field(); ?>
  <label>Username
    <input type="text" name="username" required>
  </label>
  <label>Password
    <input type="password" name="password" required minlength="6" pattern="[A-Za-z0-9]+">
  </label>
  <button type="submit">Login</button>
</form>
<?php include __DIR__ . '/../includes/footer.php'; ?>
