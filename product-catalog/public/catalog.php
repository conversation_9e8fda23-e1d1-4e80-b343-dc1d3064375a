<?php
include __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/functions.php';

$category = isset($_GET['category']) ? sanitize_category($_GET['category']) : null;
try {
    if ($category) {
        $stmt = $pdo->prepare("SELECT * FROM products WHERE category = ? ORDER BY created_at DESC");
        $stmt->execute([$category]);
    } else {
        $stmt = $pdo->query("SELECT * FROM products ORDER BY created_at DESC");
    }
    $products = $stmt->fetchAll();
} catch (Exception $e) {
    $products = [];
    $error = $e->getMessage();
}
?>
<h2>Catalog</h2>
<form method="get" class="filters">
  <label for="category">Filter by Trade:</label>
  <select name="category" id="category" onchange="this.form.submit()">
    <option value="">All</option>
    <?php
      $cats = ['TFD','Plumbing','FBW','Electrical','ICT','Welding','Carpentry','Other'];
      foreach ($cats as $cat) {
        $sel = ($category === $cat) ? 'selected' : '';
        echo "<option value='".htmlspecialchars($cat)."' $sel>".htmlspecialchars($cat)."</option>";
      }
    ?>
  </select>
</form>

<?php if (!empty($error)): ?>
  <p class="error"><?= htmlspecialchars($error) ?></p>
<?php endif; ?>

<div class="grid">
  <?php foreach ($products as $p): ?>
    <article class="card">
      <?php if (!empty($p['image_path'])): ?>
        <img src="<?= htmlspecialchars($p['image_path']) ?>" alt="<?= htmlspecialchars($p['name']) ?>">
      <?php else: ?>
        <div class="img-placeholder">No Image</div>
      <?php endif; ?>
      <div class="card-body">
        <h3><?= htmlspecialchars($p['name']) ?></h3>
        <p class="muted"><?= htmlspecialchars($p['category']) ?></p>
        <p><?= nl2br(htmlspecialchars(mb_strimwidth($p['description'],0,200,'...'))) ?></p>
        <details>
          <summary>View details</summary>
          <p><?= nl2br(htmlspecialchars($p['description'])) ?></p>
        </details>
      </div>
    </article>
  <?php endforeach; ?>
  <?php if (count($products) === 0): ?>
    <p>No products found.</p>
  <?php endif; ?>
</div>
<?php include __DIR__ . '/../includes/footer.php'; ?>
