# Deployment Checklist

## Pre-Deployment

### System Requirements
- [ ] PHP 7.4+ with SQLite extension
- [ ] Web server (Apache/Nginx) or PHP built-in server
- [ ] Write permissions for uploads directory

### File Preparation
- [ ] All required files are present (run `php test.php` to verify)
- [ ] Upload directory exists and is writable: `chmod 755 uploads/products`
- [ ] Database will be created automatically on first access

## Development Deployment

### Using PHP Built-in Server (Recommended for Testing)
```bash
cd product-catalog/public
php -S localhost:8000
```

### Access Points
- **Home Page**: http://localhost:8000/
- **Admin Panel**: http://localhost:8000/admin/dashboard.php
- **Test Script**: http://localhost:8000/../test.php

### Default Credentials
- **Username**: admin
- **Password**: Admin123

## Production Deployment

### Web Server Configuration

#### Apache (.htaccess)
```apache
# Place in public directory
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

#### Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/product-catalog/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Security
    location ~ /\. {
        deny all;
    }
}
```

### Security Hardening

#### File Permissions
```bash
# Set proper permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod 755 uploads/products
chmod 600 config.php
chmod 600 database.sqlite
```

#### Environment Variables (Optional)
Create `.env` file for sensitive configuration:
```env
DB_PATH=/secure/path/to/database.sqlite
UPLOAD_PATH=/secure/path/to/uploads
```

### Content Customization

#### Update Contact Information
- [ ] Edit `includes/header.php` - Update college contact details
- [ ] Edit `includes/footer.php` - Update footer information
- [ ] Edit `contact.php` - Update contact page content

#### Replace Placeholder Images
- [ ] `public/assets/images/logo.png` - College logo
- [ ] `public/assets/images/slide1.jpg` - Homepage slider image 1
- [ ] `public/assets/images/slide2.jpg` - Homepage slider image 2
- [ ] `public/assets/images/slide3.jpg` - Homepage slider image 3
- [ ] `public/assets/images/map.png` - College location map

#### Customize Content
- [ ] `about.php` - Update college information
- [ ] `includes/header.php` - Update site title and tagline
- [ ] `public/assets/css/style.css` - Customize colors and styling

### Post-Deployment Testing

#### Functional Tests
- [ ] Homepage loads correctly with slider
- [ ] User registration works
- [ ] User login/logout works
- [ ] Admin panel accessible
- [ ] Product CRUD operations work
- [ ] Image upload functions
- [ ] Catalog filtering works
- [ ] Search functionality works
- [ ] Contact page displays correctly

#### Security Tests
- [ ] CSRF protection active
- [ ] SQL injection prevention
- [ ] File upload restrictions
- [ ] Session security
- [ ] Password hashing

#### Performance Tests
- [ ] Page load times acceptable
- [ ] Image optimization
- [ ] Database queries optimized
- [ ] Mobile responsiveness

### Maintenance

#### Regular Tasks
- [ ] Monitor database size
- [ ] Clean up old uploaded images if needed
- [ ] Update PHP and dependencies
- [ ] Backup database regularly
- [ ] Monitor error logs

#### Backup Strategy
```bash
# Backup database
cp database.sqlite backup/database_$(date +%Y%m%d).sqlite

# Backup uploaded files
tar -czf backup/uploads_$(date +%Y%m%d).tar.gz uploads/
```

### Troubleshooting

#### Common Issues
1. **Database connection failed**
   - Check file permissions on database.sqlite
   - Ensure SQLite extension is installed

2. **Image upload not working**
   - Check uploads/products directory permissions
   - Verify PHP file upload settings

3. **Styling not loading**
   - Check web server configuration
   - Verify CSS file paths

4. **Admin panel not accessible**
   - Verify admin user exists in database
   - Check login credentials

#### Debug Mode
Add to config.php for debugging:
```php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
```

### Support

For technical issues:
1. Run the test script: `php test.php`
2. Check error logs
3. Verify all checklist items above
4. Review code comments for implementation details

---

**Deployment completed successfully!** 🎉
