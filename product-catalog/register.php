<?php
include __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    verify_csrf();
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $first = trim($_POST['first_name'] ?? '');
    $surname = trim($_POST['surname'] ?? '');
    $gender = $_POST['gender'] ?? 'Other';
    $address = trim($_POST['address'] ?? '');
    $role = $_POST['role'] ?? 'individual';

    // Validation
    $errors = [];
    if (!preg_match('/^[A-Za-z0-9_]{3,}$/', $username)) $errors[] = "Username must be at least 3 characters, alphanumeric/underscore.";
    if (!preg_match('/^[A-Za-z0-9]{6,}$/', $password)) $errors[] = "Password must be at least 6 alphanumeric characters.";
    if ($first === '' || $surname === '' || $address === '') $errors[] = "Please fill all required fields.";

    if (empty($errors)) {
        // Enforce unique username
        $exists = find_user_by_username($pdo, $username);
        if ($exists) {
            $errors[] = "Username already taken.";
        } else {
            $hash = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, password_hash, first_name, surname, gender, address, role) VALUES (?,?,?,?,?,?,?)");
            $stmt->execute([$username, $hash, $first, $surname, $gender, $address, $role]);
            header("Location: /login.php?msg=" . urlencode("Account created. Please login."));
            exit;
        }
    }
}
?>
<h2>Register</h2>
<?php if (!empty($errors)): ?>
  <div class="error">
    <ul><?php foreach ($errors as $e) echo "<li>".htmlspecialchars($e)."</li>"; ?></ul>
  </div>
<?php endif; ?>
<form method="post" class="auth-form" novalidate>
  <?php csrf_field(); ?>
  <label>First Name
    <input type="text" name="first_name" required>
  </label>
  <label>Surname
    <input type="text" name="surname" required>
  </label>
  <label>Gender
    <select name="gender" required>
      <option>Male</option>
      <option>Female</option>
      <option>Other</option>
    </select>
  </label>
  <label>Address
    <input type="text" name="address" required>
  </label>
  <label>Role
    <select name="role" required>
      <option value="institution">Institution</option>
      <option value="company">Company</option>
      <option value="individual" selected>Individual</option>
    </select>
  </label>
  <label>Username
    <input type="text" name="username" required>
  </label>
  <label>Password (min 6, alphanumeric)
    <input type="password" name="password" required minlength="6" pattern="[A-Za-z0-9]+">
  </label>
  <button type="submit">Create Account</button>
</form>
<?php include __DIR__ . '/../includes/footer.php'; ?>
