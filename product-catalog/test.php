<?php
/**
 * Simple test script to verify the Product Catalog Platform functionality
 * Run this script to check if the database and basic features are working
 */

echo "<h1>Product Catalog Platform - System Test</h1>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:2rem;} .pass{color:green;} .fail{color:red;} .info{color:blue;}</style>\n";

// Test 1: Check PHP version
echo "<h2>1. PHP Environment</h2>\n";
$phpVersion = phpversion();
echo "<p>PHP Version: <strong>$phpVersion</strong> ";
if (version_compare($phpVersion, '7.4.0', '>=')) {
    echo "<span class='pass'>✓ PASS</span></p>\n";
} else {
    echo "<span class='fail'>✗ FAIL (Requires PHP 7.4+)</span></p>\n";
}

// Test 2: Check SQLite extension
echo "<h2>2. Database Support</h2>\n";
if (extension_loaded('sqlite3')) {
    echo "<p>SQLite3 Extension: <span class='pass'>✓ Available</span></p>\n";
} else {
    echo "<p>SQLite3 Extension: <span class='fail'>✗ Not Available</span></p>\n";
}

if (extension_loaded('pdo_sqlite')) {
    echo "<p>PDO SQLite: <span class='pass'>✓ Available</span></p>\n";
} else {
    echo "<p>PDO SQLite: <span class='fail'>✗ Not Available</span></p>\n";
}

// Test 3: Database connection and setup
echo "<h2>3. Database Connection</h2>\n";
try {
    require_once __DIR__ . '/config.php';
    echo "<p>Database Connection: <span class='pass'>✓ Connected</span></p>\n";
    
    // Check if tables exist
    $tables = ['users', 'products'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='$table'");
        if ($stmt->fetch()) {
            echo "<p>Table '$table': <span class='pass'>✓ Exists</span></p>\n";
        } else {
            echo "<p>Table '$table': <span class='fail'>✗ Missing</span></p>\n";
        }
    }
    
    // Check admin user
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    if ($admin) {
        echo "<p>Admin User: <span class='pass'>✓ Created</span></p>\n";
    } else {
        echo "<p>Admin User: <span class='fail'>✗ Missing</span></p>\n";
    }
    
    // Check sample products
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $productCount = $stmt->fetch()['count'];
    echo "<p>Sample Products: <span class='info'>$productCount products found</span></p>\n";
    
} catch (Exception $e) {
    echo "<p>Database Connection: <span class='fail'>✗ Failed - " . htmlspecialchars($e->getMessage()) . "</span></p>\n";
}

// Test 4: File permissions
echo "<h2>4. File Permissions</h2>\n";
$uploadDir = __DIR__ . '/uploads/products';
if (is_dir($uploadDir)) {
    if (is_writable($uploadDir)) {
        echo "<p>Upload Directory: <span class='pass'>✓ Writable</span></p>\n";
    } else {
        echo "<p>Upload Directory: <span class='fail'>✗ Not Writable</span></p>\n";
    }
} else {
    echo "<p>Upload Directory: <span class='fail'>✗ Does not exist</span></p>\n";
}

// Test 5: Required files
echo "<h2>5. Required Files</h2>\n";
$requiredFiles = [
    'config.php',
    'includes/header.php',
    'includes/footer.php',
    'includes/functions.php',
    'public/index.php',
    'public/about.php',
    'public/catalog.php',
    'public/contact.php',
    'public/login.php',
    'public/register.php',
    'public/admin/dashboard.php',
    'public/admin/products.php',
    'public/assets/css/style.css',
    'public/assets/js/main.js'
];

$missingFiles = [];
foreach ($requiredFiles as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "<p>$file: <span class='pass'>✓</span></p>\n";
    } else {
        echo "<p>$file: <span class='fail'>✗</span></p>\n";
        $missingFiles[] = $file;
    }
}

// Test 6: Security features
echo "<h2>6. Security Features</h2>\n";
if (isset($_SESSION)) {
    echo "<p>Session Management: <span class='pass'>✓ Active</span></p>\n";
} else {
    echo "<p>Session Management: <span class='fail'>✗ Not Started</span></p>\n";
}

if (isset($_SESSION['csrf_token'])) {
    echo "<p>CSRF Protection: <span class='pass'>✓ Token Generated</span></p>\n";
} else {
    echo "<p>CSRF Protection: <span class='info'>ℹ Token will be generated on first page load</span></p>\n";
}

// Test 7: Function availability
echo "<h2>7. Core Functions</h2>\n";
if (function_exists('password_hash')) {
    echo "<p>Password Hashing: <span class='pass'>✓ Available</span></p>\n";
} else {
    echo "<p>Password Hashing: <span class='fail'>✗ Not Available</span></p>\n";
}

if (function_exists('password_verify')) {
    echo "<p>Password Verification: <span class='pass'>✓ Available</span></p>\n";
} else {
    echo "<p>Password Verification: <span class='fail'>✗ Not Available</span></p>\n";
}

// Summary
echo "<h2>Test Summary</h2>\n";
if (empty($missingFiles) && extension_loaded('pdo_sqlite') && version_compare($phpVersion, '7.4.0', '>=')) {
    echo "<p><strong><span class='pass'>✓ System Ready!</span></strong></p>\n";
    echo "<p>Your Product Catalog Platform is ready to use.</p>\n";
    echo "<p><strong>Next Steps:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Start the PHP development server: <code>cd public && php -S localhost:8000</code></li>\n";
    echo "<li>Open <a href='http://localhost:8000'>http://localhost:8000</a> in your browser</li>\n";
    echo "<li>Login with username: <strong>admin</strong>, password: <strong>Admin123</strong></li>\n";
    echo "<li>Add products through the Admin Panel</li>\n";
    echo "</ul>\n";
} else {
    echo "<p><strong><span class='fail'>✗ Issues Found</span></strong></p>\n";
    echo "<p>Please resolve the issues above before using the platform.</p>\n";
}

echo "<hr>\n";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
