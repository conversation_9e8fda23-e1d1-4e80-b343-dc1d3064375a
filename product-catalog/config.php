<?php
// Database configuration - Using SQLite for easier setup
$DB_PATH = __DIR__ . '/database.sqlite';

// PDO connection
try {
    $pdo = new PDO("sqlite:$DB_PATH", null, null, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    // Enable foreign keys for SQLite
    $pdo->exec("PRAGMA foreign_keys = ON");

    // Create tables if they don't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            first_name VA<PERSON>HA<PERSON>(100) NOT NULL,
            surname VARCHAR(100) NOT NULL,
            gender TEXT CHECK(gender IN ('Male','Female','Other')) NOT NULL,
            address VARCHAR(255) NOT NULL,
            role TEXT CHECK(role IN ('institution','company','individual','admin')) NOT NULL DEFAULT 'individual',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");

    $pdo->exec("
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(150) NOT NULL,
            category TEXT CHECK(category IN ('TFD','Plumbing','FBW','Electrical','ICT','Welding','Carpentry','Other')) NOT NULL,
            description TEXT NOT NULL,
            image_path VARCHAR(255),
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT NULL,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
        )
    ");

    // Create admin user if it doesn't exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $adminHash = password_hash('Admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password_hash, first_name, surname, gender, address, role) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin', $adminHash, 'Site', 'Admin', 'Other', 'College HQ', 'admin']);

        // Add sample products
        $adminId = $pdo->lastInsertId();
        $sampleProducts = [
            ['Steel Stool', 'Welding', 'A sturdy steel workshop stool made by trainees.'],
            ['Basic Plumbing Toolkit', 'Plumbing', 'Starter kit assembled by Plumbing trade.'],
            ['Arduino Trainer Kit', 'ICT', 'Microcontroller learning kit for first-year students.']
        ];

        $stmt = $pdo->prepare("INSERT INTO products (name, category, description, created_by) VALUES (?, ?, ?, ?)");
        foreach ($sampleProducts as $product) {
            $stmt->execute([$product[0], $product[1], $product[2], $adminId]);
        }
    }

} catch (PDOException $e) {
    die("Database connection failed: " . htmlspecialchars($e->getMessage()));
}

session_start();

// Roles helper
function is_admin() {
    return isset($_SESSION['user']) && $_SESSION['user']['role'] === 'admin';
}
function is_logged_in() {
    return isset($_SESSION['user']);
}

// CSRF
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
function csrf_field() {
    $t = htmlspecialchars($_SESSION['csrf_token']);
    echo "<input type='hidden' name='csrf_token' value='$t'>";
}
function verify_csrf() {
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        http_response_code(403);
        die('Invalid CSRF token');
    }
}
?>
