<?php
require_once __DIR__ . '/../config.php';

function find_user_by_username($pdo, $username) {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute([$username]);
    return $stmt->fetch();
}

function require_login() {
    if (!is_logged_in()) {
        header("Location: /login.php?msg=Please+login");
        exit;
    }
}

function require_admin() {
    if (!is_admin()) {
        http_response_code(403);
        die("Forbidden: Admins only.");
    }
}

function sanitize_category($cat) {
    $allowed = ['TFD','Plumbing','FBW','Electrical','ICT','Welding','Carpentry','Other'];
    return in_array($cat, $allowed, true) ? $cat : 'Other';
}

function upload_product_image($file) {
    if (!isset($file) || $file['error'] === UPLOAD_ERR_NO_FILE) {
        return null;
    }
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new RuntimeException('Upload error: ' . $file['error']);
    }
    $allowed = ['image/jpeg' => '.jpg', 'image/png' => '.png', 'image/webp' => '.webp'];
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mime = $finfo->file($file['tmp_name']);
    if (!isset($allowed[$mime])) {
        throw new RuntimeException('Invalid image type.');
    }
    $basename = bin2hex(random_bytes(8)) . $allowed[$mime];
    $dest = __DIR__ . '/../uploads/products/' . $basename;
    if (!move_uploaded_file($file['tmp_name'], $dest)) {
        throw new RuntimeException('Failed to move uploaded file.');
    }
    return '/uploads/products/' . $basename;
}
?>
