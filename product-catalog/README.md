# Technical College Product Catalog Platform

A comprehensive web application platform that allows a technical college to showcase products created by students across various trades, enabling connections with institutions, companies, individuals, and stakeholders.

## 🚀 Features

### User Management
- ✅ User registration with validation (alphanumeric passwords, unique usernames)
- ✅ Secure login/logout system with session management
- ✅ Role-based access control (Admin, Institution, Company, Individual)
- ✅ CSRF protection and password hashing

### Product Catalog
- ✅ Browse products by trade category (TFD, Plumbing, FBW, Electrical, ICT, Welding, Carpentry)
- ✅ Advanced search functionality with filters
- ✅ Detailed product views with descriptions and images
- ✅ Responsive product grid layout

### Admin Panel
- ✅ Add, edit, and delete products
- ✅ Image upload support (JPEG, PNG, WebP)
- ✅ Dashboard with statistics and recent activity
- ✅ Product management interface

### Website Pages
- ✅ **Home Page**: Rotating image slider with navigation arrows and platform description
- ✅ **About Us**: Comprehensive college information and catalog purpose
- ✅ **Catalog**: Product listings with filtering and search
- ✅ **Contact Us**: Contact information with Google Map integration

### Technical Features
- ✅ Responsive design (mobile, tablet, desktop)
- ✅ Interactive JavaScript/jQuery functionality
- ✅ SQLite database with automatic setup
- ✅ Modern CSS with animations and hover effects
- ✅ Touch/swipe support for mobile slider
- ✅ Accessibility features and print styles

## 🛠️ Installation & Setup

### Prerequisites
- PHP 7.4+ with SQLite extension
- Web server (Apache, Nginx, or PHP built-in server)

### Quick Start

1. **Navigate to the project directory**
   ```bash
   cd technical-college-product-catalog/product-catalog
   ```

2. **Set up permissions**
   ```bash
   chmod 755 uploads/products
   chmod 666 database.sqlite  # Will be created automatically
   ```

3. **Start the development server**
   ```bash
   # Using PHP built-in server (recommended for development)
   cd public
   php -S localhost:8000
   ```

4. **Access the application**
   - Open your browser and go to `http://localhost:8000`
   - Default admin credentials:
     - Username: `admin`
     - Password: `Admin123`

## 🎯 Usage Guide

### For Students/Administrators
1. **Login** with admin credentials
2. **Navigate to Admin Panel** → Manage Products
3. **Add new products** with descriptions and images
4. **Organize by trade categories**

### For Visitors
1. **Browse the catalog** without registration
2. **Filter by trade categories** or search for specific products
3. **Register an account** to inquire about products
4. **Contact the college** for orders and collaborations

### For Registered Users
1. **Login** to access enhanced features
2. **View detailed product information**
3. **Contact college** directly through the platform

## 🔒 Security Features

- **CSRF Protection**: All forms include CSRF tokens
- **Password Security**: Passwords are hashed using PHP's password_hash()
- **Input Validation**: Server-side validation for all user inputs
- **SQL Injection Prevention**: Prepared statements for all database queries
- **File Upload Security**: Restricted file types and secure file handling
- **Session Management**: Secure session handling with proper cleanup

## 📱 Browser Compatibility

- ✅ Chrome 70+
- ✅ Firefox 65+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## 🤝 Project Structure

```
product-catalog/
├── config.php              # Database configuration and security
├── database.sqlite          # SQLite database (auto-created)
├── includes/
│   ├── header.php          # Site header and navigation
│   ├── footer.php          # Site footer
│   └── functions.php       # Helper functions
├── public/                 # Web root directory
│   ├── index.php           # Home page
│   ├── about.php           # About us page
│   ├── catalog.php         # Product catalog
│   ├── contact.php         # Contact page
│   ├── login.php           # User login
│   ├── register.php        # User registration
│   ├── logout.php          # Logout handler
│   ├── admin/
│   │   ├── dashboard.php   # Admin dashboard
│   │   └── products.php    # Product management
│   └── assets/
│       ├── css/style.css   # Main stylesheet
│       ├── js/main.js      # JavaScript functionality
│       └── images/         # Site images
└── uploads/
    └── products/           # Uploaded product images
```

---

**Built with ❤️ for Technical College Education**
*Generated on 2025-08-27*
