<?php
include __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/functions.php';

// Get recent products for display
try {
    $stmt = $pdo->query("SELECT * FROM products ORDER BY created_at DESC LIMIT 6");
    $recent_products = $stmt->fetchAll();
} catch (Exception $e) {
    $recent_products = [];
}

// Success message from login/logout
$msg = isset($_GET['msg']) ? $_GET['msg'] : null;
?>

<?php if (!empty($msg)): ?>
<div class="success mb-1"><?= htmlspecialchars($msg) ?></div>
<?php endif; ?>

<section class="hero">
  <div class="slider" id="slider">
    <div class="slides">
      <img src="/assets/images/slide1.jpg" alt="Workshop products">
      <img src="/assets/images/slide2.jpg" alt="Students in action">
      <img src="/assets/images/slide3.jpg" alt="Technical excellence">
    </div>
    <button class="nav prev" id="prev" aria-label="Previous slide">‹</button>
    <button class="nav next" id="next" aria-label="Next slide">›</button>
  </div>
  <div class="text-center mt-1">
    <h2>Welcome to Technical College Product Catalog</h2>
    <p class="lead">Discover exceptional products crafted by our skilled students across multiple trades. From welding masterpieces to innovative ICT solutions, explore the future of technical excellence.</p>
    <a href="/catalog.php" class="btn">Browse Catalog</a>
    <?php if (!is_logged_in()): ?>
      <a href="/register.php" class="btn btn-secondary">Join Our Community</a>
    <?php endif; ?>
  </div>
</section>

<?php if (!empty($recent_products)): ?>
<section class="card mt-1">
  <h3>Recent Products</h3>
  <div class="grid">
    <?php foreach ($recent_products as $p): ?>
      <article class="card">
        <?php if (!empty($p['image_path'])): ?>
          <img src="<?= htmlspecialchars($p['image_path']) ?>" alt="<?= htmlspecialchars($p['name']) ?>">
        <?php else: ?>
          <div class="img-placeholder">No Image</div>
        <?php endif; ?>
        <div class="card-body">
          <h4><?= htmlspecialchars($p['name']) ?></h4>
          <p class="muted"><?= htmlspecialchars($p['category']) ?></p>
          <p><?= nl2br(htmlspecialchars(mb_strimwidth($p['description'],0,100,'...'))) ?></p>
        </div>
      </article>
    <?php endforeach; ?>
  </div>
  <div class="text-center mt-1">
    <a href="/catalog.php" class="btn">View All Products</a>
  </div>
</section>
<?php endif; ?>

<section class="card mt-1">
  <h3>Our Trades</h3>
  <div class="grid">
    <?php
    $trades = [
      'TFD' => 'Technical and Further Development - Advanced technical skills and innovation',
      'Plumbing' => 'Professional plumbing solutions and installations',
      'FBW' => 'Food, Beverage and Woodwork - Craftsmanship and culinary arts',
      'Electrical' => 'Electrical installations and electronic solutions',
      'ICT' => 'Information and Communication Technology innovations',
      'Welding' => 'Metal fabrication and welding expertise',
      'Carpentry' => 'Woodworking and furniture craftsmanship'
    ];
    foreach ($trades as $trade => $desc): ?>
      <div class="card">
        <h4><?= htmlspecialchars($trade) ?></h4>
        <p><?= htmlspecialchars($desc) ?></p>
        <a href="/catalog.php?category=<?= urlencode($trade) ?>" class="btn">View Products</a>
      </div>
    <?php endforeach; ?>
  </div>
</section>

<?php include __DIR__ . '/../includes/footer.php'; ?>
