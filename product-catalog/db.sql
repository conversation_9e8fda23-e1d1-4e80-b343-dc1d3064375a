-- MySQL schema and sample data for Product Catalog Platform
CREATE DATABASE IF NOT EXISTS product_catalog CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE product_catalog;

-- Users
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
  surname VARCHAR(100) NOT NULL,
  gender ENUM('Male','Female','Other') NOT NULL,
  address VARCHAR(255) NOT NULL,
  role ENUM('institution','company','individual','admin') NOT NULL DEFAULT 'individual',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Products
CREATE TABLE IF NOT EXISTS products (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(150) NOT NULL,
  category ENUM('TFD','Plumbing','FBW','Electrical','ICT','Welding','Carpentry','Other') NOT NULL,
  description TEXT NOT NULL,
  image_path VARCHAR(255),
  created_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Seed Admin (username: admin, password: Admin123)
INSERT INTO users (username, password_hash, first_name, surname, gender, address, role)
VALUES (
  'admin',
  -- password_hash('Admin123', PASSWORD_DEFAULT)
  '$2y$10$1xF1Cq5Sxj2nueF2jZfO3OMmD4rUjJ9r8U4R3g3tZCqH0kZtqg2yK',
  'Site',
  'Admin',
  'Other',
  'College HQ',
  'admin'
) ON DUPLICATE KEY UPDATE username=username;

-- Sample products (optional)
INSERT INTO products (name, category, description, image_path, created_by)
VALUES
('Steel Stool', 'Welding', 'A sturdy steel workshop stool made by trainees.', NULL, 1),
('Basic Plumbing Toolkit', 'Plumbing', 'Starter kit assembled by Plumbing trade.', NULL, 1),
('Arduino Trainer Kit', 'ICT', 'Microcontroller learning kit for first-year students.', NULL, 1)
;
